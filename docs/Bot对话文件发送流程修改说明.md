# Bot对话文件发送流程修改说明

## 修改概述

针对发送给bot的多轮对话场景，实现了新的文件处理流程，其他场景保持现状不变。

## 修改内容

### 1. 判断Bot对话场景

在 `MessageInput` 组件的 `enterToSend` 方法中，添加了Bot对话场景的判断逻辑：

```typescript
// 检查是否为bot对话场景
const isBotConversation =
  currentConversation?.userID &&
  userDetail?.ex &&
  JSON.parse(userDetail?.ex || '{}')?.stream === true;
```

### 2. 新增 `fileSendForBot` 方法

专门为Bot对话场景创建的文件发送方法：

```typescript
// Bot对话场景的文件发送：先上传文件获取URL，再发送文件消息
const fileSendForBot = async (list: FileListType[]) => {
  for (const mesItem of list) {
    if (!mesItem.isClouDoc && mesItem.file) {
      try {
        // 1. 创建文件消息（使用专门的Bot方法）
        const message = await createFileMessageForBot(mesItem.file);
        
        // 2. 上传文件获取URL
        const uploadResult = await IMSDK.uploadFileByMessage(message);
        
        // 3. 将URL放入消息的s3Path字段
        const messageWithS3Path = {
          ...message,
          s3Path: uploadResult.data?.url || uploadResult.url,
        };
        
        // 4. 发送文件消息
        await sendMessage({ message: messageWithS3Path });
      } catch (error) {
        console.error('Bot文件上传失败:', error);
        // 如果上传失败，仍然发送原始文件消息
        const message = await createFileMessageForBot(mesItem.file);
        await sendMessage({ message });
      }
    }
  }
};
```

### 3. 新增 `createFileMessageForBot` 方法

在 `useFileMessage.ts` 中添加专门为Bot对话场景创建文件消息的方法：

```typescript
// 为Bot对话场景创建文件消息的方法
const createFileMessageForBot = async (
  file: FileWithPath
): Promise<MessageItem> => {
  const isImage = canSendImageTypeList.includes(getFileType(file.name));
  
  if (isImage) {
    // 对于图片，调用 createImageMessage
    return getImageMessage(file);
  } else {
    // 对于其他文件，调用 createFileMessage
    return getFileMessage(file);
  }
};
```

### 4. 修改 `enterToSend` 流程

更新了消息发送的主流程：

```typescript
if (cleanText) {
  if (isBotConversation) {
    // Bot对话场景：先处理文件，再发送流式消息
    if (fileList && fileList.length > 0) {
      await fileSendForBot(fileList);
    }
    
    message = (await IMSDK.createTextMessage(cleanText)).data;
    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
    });
    clear();
    return;
  } else {
    // 非Bot对话场景：保持原有逻辑
    // ... 原有逻辑不变
  }
}

// 非Bot对话场景的文件处理
if (!isBotConversation && fileList && fileList.length > 0) {
  fileSend(fileList);
}
```

## 新的流程说明

### Bot对话场景下的文件发送流程：

1. **文件选择**: 用户在messageInput中选择文件（不支持云文档）
2. **创建消息**: 调用 `createFileMessageForBot` 方法
   - 图片文件：调用 `getImageMessage` (对应 `createImageMessage`)
   - 其他文件：调用 `getFileMessage` (对应 `createFileMessage`)
3. **文件上传**: 调用 `IMSDK.uploadFileByMessage` 方法上传文件
4. **获取URL**: 从上传结果中获取URL，放入消息的 `s3Path` 字段
5. **发送文件消息**: 调用 `sendMessage` 发送包含s3Path的文件消息
6. **发送流式消息**: 最后调用 `sendStreamMessage` 发送流式消息

### 非Bot对话场景：

保持原有的文件发送逻辑不变，确保向后兼容。

## 错误处理

- 如果文件上传失败，会fallback到发送原始文件消息
- 所有错误都会被捕获并记录到控制台
- 不会影响文本消息的正常发送

## 兼容性

- 非Bot对话场景完全保持原有逻辑
- 只有在检测到Bot对话场景（`stream=true`）时才会使用新流程
- 云文档发送在Bot场景下被跳过（按需求不支持）

## API依赖

新流程依赖以下API：
- `IMSDK.uploadFileByMessage(message)` - 文件上传接口
- 返回格式：`{ data: { url: string } }` 或 `{ url: string }`

## 测试建议

1. 测试Bot对话场景下的图片发送
2. 测试Bot对话场景下的文件发送
3. 测试文件上传失败的fallback逻辑
4. 确认非Bot场景的文件发送不受影响
5. 测试同时发送文本和文件的情况
