/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { mentionRegex } from '@/components/MdEditor/plugin-mention/MentionShecma';
import { selectedTextRef } from '@/hooks/useSelectedText';
import { WINDOW_TYPE } from '@/pages/browserWin/config';
import { parserMdToHtml } from './parserMdToHtml';
import { feedbackToast } from './common';

export const getMessageContent = (message: MessageItemType): string => {
  switch (message.contentType) {
    case MessageType.TextMessage:
      return message.textElem?.content || '';
    case MessageType.QuoteMessage:
      return message.quoteElem?.text || '';
    case MessageType.AtTextMessage:
      return message.atTextElem?.text || '';
    case MessageType.MergeMessage:
      return message.mergeElem?.title || '';
    case MessageType.CustomMessage:
      return message.customElem?.data
        ? JSON.parse(message.customElem.data).content.answer
        : '';
    case MessageType.GroupAnnouncementUpdated:
      // eslint-disable-next-line no-case-declarations
      const groupAnnouncementDetails = JSON.parse(
        message.notificationElem!.detail
      );
      // eslint-disable-next-line no-case-declarations
      const { group } = groupAnnouncementDetails || {};
      return group?.notification ? `群公告：\n${group.notification}` : '';
    default:
      return '';
  }
};

export const handleCopy = (message: MessageItemType, copyAll = false) => {
  const messageText = getMessageContent(message);
  let content = '';
  if (copyAll) {
    content = parserMdToHtml(messageText.replace(mentionRegex, '@$1') || '');
  } else {
    content = selectedTextRef.current;
  }

  // 处理代码块的复制逻辑：当div类名为code-block-formate-wrapper时，只复制下面的pre标签
  content = handleCodeBlockCopy(content);

  copyContent(content);
};

/**
 * 处理代码块的复制逻辑
 * 当内容中包含code-block-formate-wrapper类名的div时，只复制其中的pre标签内容
 * @param content HTML内容
 * @returns 处理后的HTML内容
 */
export const handleCodeBlockCopy = (content: string): string => {
  if (!content || typeof content !== 'string') {
    return content;
  }

  // 创建一个临时的DOM元素来解析HTML
  const tempElement = document.createElement('div');
  tempElement.innerHTML = content;

  // 查找所有code-block-formate-wrapper类的div
  const codeBlockWrappers = tempElement.querySelectorAll(
    '.code-block-formate-wrapper'
  );

  // 如果没有找到code-block-formate-wrapper，直接返回原内容
  if (codeBlockWrappers.length === 0) {
    return content;
  }

  // 处理每个code-block-formate-wrapper
  codeBlockWrappers.forEach((wrapper) => {
    // 查找pre标签
    const preElement = wrapper.querySelector('pre');

    if (preElement) {
      // 克隆pre元素并确保它是可见的
      const clonedPre = preElement.cloneNode(true) as HTMLElement;
      if (clonedPre.style.display === 'none') {
        clonedPre.style.display = 'block';
      }

      // 替换wrapper的内容为pre标签的内容
      wrapper.parentNode?.replaceChild(clonedPre, wrapper);
    }
  });

  return tempElement.innerHTML;
};

export const copyContent = (content: string) => {
  const clipboardData = new DataTransfer();
  clipboardData.setData('text/html', content);
  clipboardData.setData('text/plain', content.replace(/<[^>]+>/g, ''));
  let flag = true;
  try {
    navigator.clipboard.write([
      new ClipboardItem({
        'text/html': new Blob([content], { type: 'text/html' }),
        'text/plain': new Blob([content.replace(/<[^>]+>/g, '')], {
          type: 'text/plain',
        }),
      }),
    ]);
  } catch (error) {
    console.log('catch', error);
    flag = unsecuredCopyToClipboard(content);
  }
  if (flag) {
    feedbackToast({ msg: '复制成功' });
  } else {
    feedbackToast({ error: '复制失败' });
  }
};

const unsecuredCopyToClipboard = (text: string) => {
  // 降级方案2：使用传统的 execCommand
  const tempElement = document.createElement('div');
  tempElement.innerHTML = text;

  // 设置样式确保元素可选择但不可见
  tempElement.style.position = 'absolute';
  tempElement.style.left = '-9999px';
  tempElement.style.top = '-9999px';
  tempElement.style.width = '1px';
  tempElement.style.height = '1px';
  tempElement.style.opacity = '0';
  tempElement.style.overflow = 'hidden';
  tempElement.style.whiteSpace = 'pre'; // 保持代码格式

  document.body.appendChild(tempElement);

  // Create a range and select the content
  const range = document.createRange();
  range.selectNodeContents(tempElement);

  const selection = window.getSelection();
  selection?.removeAllRanges();
  selection?.addRange(range);

  // Execute the copy command
  const successful = document.execCommand('copy');
  if (!successful) {
    console.error('Failed to copy text using execCommand');
    return false;
  }

  // Clean up
  selection?.removeAllRanges();
  document.body.removeChild(tempElement);
  return true;
};

// 处理客户端点击图片预览
export const openImgPreviewWin = async (sourcePicture, callback = () => {}) => {
  const {
    url = '',
    width: picWidth,
    height: picHeight,
    imgWinId = '',
  } = sourcePicture;
  if (!url) {
    return;
  }
  if (window.electronAPI) {
    const browserWindow = window?.htElectronSDK?.BrowserWindow;
    const filepath = await browserWindow?.imageLocalUrl(url);
    if (filepath && !browserWindow?.existsFileLocal(filepath)) {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const blob = await response.blob();
      const arrayBuffer = await blob.arrayBuffer(); // 转为 ArrayBuffer
      const buffer = Buffer.from(arrayBuffer); // 转换为 Buffer
      browserWindow?.writeImageLocal(filepath, buffer);
    }
    const winId = await browserWindow?.showBrowserWin({
      type: WINDOW_TYPE.imgPreview,
      title: '图片预览',
      option: {
        width: picWidth,
        height: picHeight,
        minWidth: 400,
        minHeight: 300,
      },
      params: { filepath, isSingle: false, imgWinId },
    });
    return winId;
  } else {
    callback?.();
  }
};
