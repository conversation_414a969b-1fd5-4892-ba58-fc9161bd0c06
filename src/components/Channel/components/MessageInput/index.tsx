/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/restrict-template-expressions */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
/* eslint-disable indent */
import { useState, useRef, useEffect, useMemo } from 'react';
import { getCleanText } from '@/components/CKEditor/utils';
import { useDeepCompareEffect } from 'ahooks';
import { useSendMessage } from '@/hooks/useSendMessage';
import { IMSDK } from '@/layouts/BasicLayout';
import { notification, message as Message } from '@ht/sprite-ui';
import {
  useConversationStore,
  useGlobalModalStore,
  useUserStore,
} from '@/store';
import {
  BotCommandItem,
  ConversationItem,
  MessageItem,
  GroupStatus,
} from '@ht/openim-wasm-client-sdk';
import { getFileType, canSendImageTypeList } from '@/utils/common';
import { useFileMessage } from '@/hooks/useFileMessage';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/react';
import { EditorProvider } from '@/components/MdEditor/editorContent';
import { v4 as uuidv4 } from 'uuid';
import _, { debounce, isNil, isEmpty } from 'lodash';
import emitter from '@/utils/events';
import sendIcon from '@/assets/channel/messageInput/send.png';
import stopIcon from '@/assets/channel/messageInput/stop.svg';
import quoteDeleteIcon from '@/assets/channel/messageInput/quoteDelete.svg';
import configSetIcon from '@/assets/channel/robotConversationList/configSet.svg';
import { EditMd } from '@/components/MdEditor';
import { useKeyboardEvent } from '@/hooks/useKeyboardEvent';
import { KeyboardEventSource, charKeyCodes } from '@/utils/constants';
import useGroupMembers from '@/hooks/useGroupMembers';
import { useRobot } from '@/hooks/useRobot';
import classNames from 'classnames';
import {
  getStatusImgSrcByStatus,
  getShowDescByStatus,
} from '@/components/UserState/SetStateModal/const';
import useUserInfo from '@/hooks/useUserInfo';
import { shallow } from 'zustand/shallow';
import { RobotCommand } from './RobotCommand';
import QuoteMessageRender from '../MessageItem/QuoteMessageRender';
import FileRender from './FileRender';
import RobotConfigModal from '../MessageListForeword/RobotConfigModal';
import styles from './index.less';
import TextOperate from './TextOperate';

export interface FileListType {
  type: string;
  file?: File;
  id: string;
  url: string;
  docInfo?: any;
  isImage?: boolean;
  isClouDoc?: boolean;
}

// 判断是不是指令
export const startsWithSlashNoSpace = (str: string) => {
  return /^\/(?!\s)/.test(str);
};

const defaultFormatterDisplayPref =
  localStorage.getItem('linkim.messageInput.formatterDisplayPref') !== 'false';

export interface editMdRefType {
  clearValue: () => void;
  focus: () => void;
  getValue: () => void;
  getMentionList: () => string[];
  getNodeJson: () => void;
  activeMarks: Set<string>;
}

interface robotCommandRefProps {
  manuallySubmit: () => void;
  getFormState: () => any;
}

type MessasgeInputProps = {
  // threadID?: string;
  inRightThread?: boolean;
  conversation: ConversationItem | undefined;
  multiSessionDataIsEmpty?: boolean;
};

type DraftTextProps = {
  value: string;
  replyMsg: string;
  atUserList: string[];
  fileList: FileListType[];
  selectedCommand: BotCommandItem | null;
  formInfo: {
    values: any;
    showMore: boolean;
  };
};
const MessageInput = (props: MessasgeInputProps) => {
  const {
    inRightThread,
    conversation,
    multiSessionDataIsEmpty = false,
  } = props;
  const [value, setValue] = useState('');
  const [replyMsg, setReplyMsg] = useState<MessageItem>();
  const { sendMessage, sendThreadMessage, sendStreamMessage } =
    useSendMessage(conversation);
  const rightAreaInGroupConversation = useConversationStore(
    (state) => state.rightAreaInGroupConversation
  );

  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const llmLoading = useConversationStore((state) => state.llmLoading);

  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  const updateLlmLoading = useConversationStore(
    (state) => state.updateLlmLoading
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const botConfig = useConversationStore((state) => state.botConfig);
  const currentBotConfig = useConversationStore(
    (state) => state.currentBotConfig
  );
  const botConfigModal = useConversationStore((state) => state.botConfigModal);
  const updateBotConfigModal = useConversationStore(
    (state) => state.updateBotConfigModal
  );

  const setCurrentMessageInputValue = useConversationStore(
    (state) => state.setCurrentMessageInputValue
  );

  const isMultiSessionCreate =
    isMultiSession &&
    isEmpty(currentBotConfig) &&
    multiSessionDataIsEmpty &&
    botConfig?.length > 0;
  const showMultiSessionSet =
    isMultiSession &&
    (!multiSessionDataIsEmpty || !isEmpty(currentBotConfig)) &&
    botConfig?.length > 0;

  const [hasInit, setHasInit] = useState(false);
  const [draftText, setDraftText] = useState<DraftTextProps | undefined>(
    undefined
  );

  const { draftText: draftTextStr } = currentConversation || {};
  useEffect(() => {
    try {
      const draftJson = draftTextStr ? JSON.parse(draftTextStr) : null;
      //
      if (!hasInit) {
        if (draftJson != null && draftJson !== '') {
          setDraftText(draftJson);
          setCurrentMessageInputValue(draftJson);
        } else {
          setDraftText(undefined);
          setHasInit(true);
          setCurrentMessageInputValue(null);
        }
      }
    } catch (e) {
      setDraftText(undefined);
      setHasInit(true);
    }
  }, [
    currentConversation,
    draftText,
    draftTextStr,
    hasInit,
    setCurrentMessageInputValue,
  ]);

  const setNetErrorTooltipOpen = useGlobalModalStore(
    (state) => state.setNetErrorTooltipOpen
  );

  const { userState, userDetail } = useUserInfo(currentConversation?.userID);

  const [isComposition, setIsComposition] = useState<boolean>(false);
  const [typing, setTyping] = useState<boolean>(false);
  const [fileList, setFileList] = useState<FileListType[]>([]);
  const [showFormater, setShowFormater] = useState<boolean>(
    !!defaultFormatterDisplayPref
  );
  // const [mentionArr, setMentionArr] = useState<GroupMemberItem[]>([]);
  const { createFileMessage } = useFileMessage();
  const editMdRef = useRef<editMdRefType>();
  const { fetchState, getMemberData, resetState } =
    useGroupMembers(conversation);
  const messageInputRef = useRef();
  const pasteContainerRef = useRef();
  const { submitRobotCommand } = useRobot(conversation?.groupID as string);

  const { syncState, connectState } = useUserStore(
    (state) => ({
      syncState: state.syncState,
      connectState: state.connectState,
    }),
    shallow
  );

  const showConnecting =
    syncState === 'success' &&
    (connectState === 'loading' || connectState === 'failed');

  const handleEnter = () => {
    if (showCommandList || selectedCommand?.options?.length) {
      return;
    }
    const val = editMdRef.current?.getValue() || value;
    enterToSend(val);
  };

  const [placeholderText, setPlaceholderText] = useState<any>('');
  const [showCommandList, setShowCommandList] = useState(false);
  const [selectedCommand, setSelectedCommand] =
    useState<BotCommandItem | null>();

  const [formInfo, setFormInfo] = useState<DraftTextProps['formInfo']>();

  const robotCommandRef = useRef<robotCommandRefProps>();

  const handleKeyDown = useKeyboardEvent(
    KeyboardEventSource.MESSAGE_INPUT,
    {
      onEnter: () => {
        // 如果是ctrl+enter，不处理

        if (!isComposition) {
          handleEnter();
        }
      },
      onEvent: (e) => {
        // 打0-9 a-z时设置正在输入flag,隐藏placeholder
        setTyping(charKeyCodes.includes(e.keyCode));
      },
    },
    {
      shouldHandle: !selectedCommand,
      shouldPreventDefault: true,
    }
  );
  useEffect(() => {
    // 精准匹配才显示指令modal
    if (startsWithSlashNoSpace(value.trim())) {
      setShowCommandList(true);
    } else {
      setShowCommandList(false);
    }
  }, [value]);

  useEffect(() => {
    if (draftText != null) {
      setPlaceholderText('');
      return;
    }
    if (conversation?.groupID) {
      if (currentGroupInfo?.status === GroupStatus.Muted) {
        setPlaceholderText(<>当前群聊已禁言，仅群主及管理员可以发消息</>);
      } else {
        setPlaceholderText(<>{`向 #${conversation?.showName} 发送消息`}</>);
      }
    } else {
      const personStatus = (userState?.code || userState?.desc) && (
        <span className={styles.state}>
          <img src={getStatusImgSrcByStatus(userState)} />
          <span>{getShowDescByStatus(userState)}</span>
        </span>
      );
      setPlaceholderText(
        <>
          给 {conversation?.showName} {personStatus} 发送消息
        </>
      );
    }
  }, [conversation, userState, currentGroupInfo, hasInit, draftText]);

  const grouMemberList = useMemo(() => {
    return conversation?.groupID
      ? [
          {
            nickname: `所有人`,
            userID: 'AtAllTag',
          },
        ].concat(
          fetchState.groupMemberList.filter(
            (member) => member.userID !== currentMemberInGroup?.userID
          )
        )
      : [];
  }, [
    conversation?.groupID,
    currentMemberInGroup?.userID,
    fetchState.groupMemberList,
  ]);

  useDeepCompareEffect(() => {
    if (llmLoading) {
      updateLlmLoading(false);
    }
    clear();
    emitter.on('REPLAYMSG', replyHandler);
    return () => {
      emitter.off('REPLAYMSG', replyHandler);
    };
  }, [conversation?.conversationID]);

  useDeepCompareEffect(() => {
    getMemberData(true);
    return () => {
      resetState();
    };
  }, [conversation?.conversationID]);

  const clear = () => {
    editMdRef.current?.clearValue();
    setValue('');
    setReplyMsg(undefined);
    setFileList([]);
  };

  const streamBreak = async () => {
    IMSDK.stopMsgToBot({
      conversationID: conversation?.conversationID,
    })
      .then((res) => {
        if (res?.data) {
          updateLlmLoading(false);
        }
      })
      .catch((err) => {
        console.error('stopMsgToBot', err);
      });
  };

  const enterToSend = async (html: string) => {
    if (html.length > 2000) {
      notification.open({
        message: '消息超长',
        description: '单条消息最长可发送2000字，请拆分后再尝试',
      });
      return;
    }
    if (llmLoading || showCommandList) {
      return;
    }

    if (showConnecting) {
      setNetErrorTooltipOpen(true);
      return;
    }
    if (
      conversation?.groupID &&
      (!currentMemberInGroup || !currentMemberInGroup.userID) // 群聊用户不在组的情况
    ) {
      return;
    }

    const cleanText = getCleanText(html);
    let message;

    if (cleanText) {
      if (currentConversation?.userID) {
        if (userDetail?.ex) {
          const exInfo = JSON.parse(userDetail?.ex) || {};
          const { stream = false } = exInfo || {};
          if (stream) {
            message = (await IMSDK.createTextMessage(cleanText)).data;
            sendStreamMessage({
              recvUser: userDetail,
              curMsg: message,
            });
            clear();
            return;
          }
        }
      }
      const atUserList = editMdRef?.current?.getMentionList();
      if (atUserList && atUserList.length > 0) {
        message = await getAtMessage(atUserList, cleanText);
      } else if (replyMsg) {
        // 创建引用消息
        message = (
          await IMSDK.createQuoteMessage({
            text: cleanText,
            message: JSON.stringify(replyMsg),
          })
        ).data;
      } else {
        message = (await IMSDK.createTextMessage(cleanText)).data;
      }

      if (rightAreaInGroupConversation === 'thread') {
        sendThreadMessage(message, inRightThread);
      } else {
        sendMessage({ message });
      }
    }

    if (fileList && fileList.length > 0) {
      fileSend(fileList);
    }

    setCurrentMessageInputValue(null);
    setDraftText(undefined);

    setShowCommandList(false);

    setFormInfo(undefined);

    clear();
  };

  const debounceFetch = debounce(enterToSend, 300);

  // @消息
  const getAtMessage = async (atUserList: string[], cleanText: string) => {
    const atUserIDList = [];
    const atUsersInfo = [];
    const idx = atUserList.findIndex((item) => item === 'AtAllTag');
    if (idx > -1) {
      atUserList.splice(idx, 1);
      atUserIDList.push('AtAllTag');
      atUsersInfo.push({
        atUserID: 'AtAllTag',
        groupNickname: '所有人',
      });
    }
    const { data } = await IMSDK.getUsersInfo(atUserList);
    for (const user of data) {
      atUserIDList.push(user.userID);
      atUsersInfo.push({
        atUserID: user.userID,
        groupNickname: user.nickname,
      });
    }
    const params = {
      text: cleanText,
      atUserIDList,
      atUsersInfo,
    };
    if (replyMsg) {
      params.message = replyMsg;
    }
    const message = (await IMSDK.createTextAtMessage(params)).data;
    return message;
  };

  // 文件/图片/云文档
  const fileSend = async (list: FileListType[]) => {
    for (const mesItem of list) {
      if (mesItem.isClouDoc) {
        const { data } = await IMSDK.createCustomMessage({
          data: JSON.stringify({
            type: 'clouddocument',
            content: {
              ...mesItem.docInfo,
            },
          }),
          extension: '',
          description: '',
        });
        sendMessage({
          message: data,
        });
      } else {
        const data = await createFileMessage(mesItem?.file);
        sendMessage({
          message: data,
        });
      }
    }
  };

  const handleCompositionStart = () => {
    setIsComposition(true);
  };

  const handleCompositionEnd = () => {
    setIsComposition(false);
  };

  const replyHandler = (msg: MessageItem) => {
    if (
      (conversation && !conversation?.groupID && !msg.groupID) ||
      (msg.groupID && msg.groupID === conversation?.groupID)
    ) {
      setReplyMsg(msg);
      if (msg) {
        setShowCommandList(false);
        setSelectedCommand(null);
        setFormInfo(undefined);
      }
      editMdRef.current?.focus();
    }
  };

  // 缓存下，避免头像img不停的闪烁
  const ReplyPrefix = useMemo(() => {
    return replyMsg ? (
      <div className={styles.replyBox}>
        <div>
          <QuoteMessageRender message={replyMsg} isInput={true} />
        </div>
        <div className={styles.removeReplyMsg}>
          <img src={quoteDeleteIcon} onClick={() => setReplyMsg(undefined)} />
        </div>
      </div>
    ) : null;
  }, [replyMsg]);

  const uploadMsg = async (uploadData: any, type: string, path: any) => {
    if (isEmpty(path)) {
      const fileEl = uploadData.file as File;
      if (fileEl.size === 0) {
        const text = `${fileEl.name}是一个空文件，不能发送`;
        Message.info(text);
        return;
      }
      if (fileEl.size > 50 * 1024 * 1024) {
        Message.info('仅可发送50M以下的文件');
        return;
      }
      const isImage = canSendImageTypeList.includes(getFileType(fileEl.name));
      let fileUrl = '';
      if (isImage) {
        if (window.electronAPI) {
          fileUrl = fileEl?.previewUrl || '';
        } else {
          fileUrl = URL.createObjectURL(fileEl);
        }
      }
      console.debug({ fileEl });

      setFileList((pre) => {
        return [
          ...pre,
          {
            type,
            file: fileEl,
            id: uuidv4(),
            url: fileUrl,
            isImage,
          },
        ];
      });
    } else {
      setFileList((pre) => {
        return [
          ...pre,
          {
            type: 'file',
            file: {
              name: 'image.png',
              type: 'image/png',
              lastModified: Date.now(),
              path,
            },
            id: uuidv4(),
            url: URL.createObjectURL(uploadData),
            isImage: true,
          },
        ];
      });
    }
    editMdRef.current?.focus();
  };

  const paste = (e: any) => {
    // if (!currentMemberInGroup || !currentMemberInGroup.userID) {
    //   return;
    // }
    const { items } = e.clipboardData || e.originalEvent.clipboardData;
    for (const file of items) {
      const fileType = file.type;
      if (fileType.includes('text')) {
      } else {
        const fileEl = file.getAsFile();
        if (fileEl.size === 0) {
          return;
        }

        const isImage = canSendImageTypeList.includes(getFileType(fileEl.name));
        setFileList((pre) => {
          return [
            ...pre,
            {
              type: 'file',
              file: fileEl,
              id: uuidv4(),
              url: isImage ? URL.createObjectURL(fileEl) : '',
              isImage,
            },
          ];
        });
      }
    }
  };

  const botSubmit = async (values: any, sessionType: number) => {
    const { command, ...data } = values;
    // // 将data中的数据整合为duration: 1days winners: 1 prize: 200 格式，拼接在message中,没值的就不加了
    // const message = `/${command.name} ${Object.entries(data)
    //   .filter(
    //     ([_, value]) => value !== undefined && value !== null && value !== ''
    //   )
    //   .map(([key, value]) => `${key}: ${value}`)
    //   .join(' ')}`;

    let message = `/${command.name}`;
    if (!isEmpty(data)) {
      const options: any[] = command.options || [];
      const obj: any = {};
      // eslint-disable-next-line array-callback-return
      Object.keys(data).map((key) => {
        const item = options.filter((val: any) => val.key === key);
        const label = item[0].name;
        obj[label] = data[key];
      });
      message = `/${command.name} ${JSON.stringify(obj)}`;
    }
    // 先发消息，消息成功后再发机器人消息
    try {
      await enterToSend(`${message}`);
      // 发送消息成功后再调用机器人交互
      await submitRobotCommand({
        userID: currentMemberInGroup?.userID,
        groupID: conversation?.groupID,
        botID: command.botID,
        actionID: command.key,
        actionName: command.name,
        type: 1,
        sessionType,
        data,
      });
    } catch (error) {
      console.error('发送机器人指令失败:', error);
    }
  };

  const setTypingStatus = async (status: boolean) => {
    try {
      if (conversation?.conversationID != null) {
        await IMSDK.changeInputStates({
          conversationID: conversation?.conversationID,
          focus: status,
        });
      }
    } catch (e) {
      console.error('更新输入状态失败', e);
    }
  };

  const cloudUpload = async (docInfo: any) => {
    setFileList((pre) => {
      return [
        ...pre,
        {
          type: 'cloudDoc',
          id: uuidv4(),
          url: '',
          docInfo,
          isClouDoc: true,
        },
      ];
    });
    editMdRef.current?.focus();
  };

  // 监听变化，上报草稿数据
  useEffect(() => {
    try {
      if (editMdRef?.current != null && hasInit) {
        const atUserList = editMdRef?.current?.getMentionList();

        const draftFileList = fileList?.filter(
          (item) => !item.isImage && item.type !== 'file'
        );

        if (
          value === '' &&
          isEmpty(atUserList) &&
          isNil(replyMsg) &&
          isEmpty(draftFileList) &&
          isNil(selectedCommand) &&
          isNil(formInfo)
        ) {
          setCurrentMessageInputValue(null);
          return;
        }

        setCurrentMessageInputValue({
          value: value === '' ? '' : editMdRef?.current?.getNodeJson(),
          atUserList,
          replyMsg,
          fileList: draftFileList,
          selectedCommand,
          formInfo,
        });
      }
    } catch (e) {
      // 尚未init成功
    }
  }, [
    value,
    replyMsg,
    fileList,
    hasInit,
    setCurrentMessageInputValue,
    currentConversation,
    selectedCommand,
    formInfo,
  ]);

  // 根据草稿恢复内容
  useEffect(() => {
    if (hasInit) {
    } else if (draftText == null) {
    } else {
      try {
        const {
          replyMsg: draftReplyMsg,
          fileList: draftFileList,
          selectedCommand: draftSelectedCommand,
          formInfo: draftFormInfo,
        } = draftText;
        // 0、MD编辑器的文本用default恢复
        // 1、过滤图片和本地文件，只保留云文档

        if (draftFileList != null && draftFileList?.length !== 0) {
          const toDraftFileList = draftFileList?.filter(
            (item: FileListType) => !item.isImage && item.type !== 'file'
          );

          setFileList([...toDraftFileList]);
        }
        // 2、恢复引用消息
        if (draftReplyMsg != null && draftReplyMsg !== '') {
          setReplyMsg(draftReplyMsg as unknown as MessageItem);
        }

        // 3、恢复指令消息
        if (draftSelectedCommand != null) {
          setShowCommandList(false);
          setSelectedCommand(draftSelectedCommand);

          // 4、恢复指令表单内容
          if (draftFormInfo != null) {
            setFormInfo(draftFormInfo);
          }
        }
      } catch (e) {}

      setHasInit(true);
    }
  }, [draftText, formInfo, hasInit]);

  return (
    <div
      className={styles.content}
      style={
        isMultiSessionCreate
          ? { display: 'none' }
          : {
              minHeight: !isMultiSession ? '120px' : '80px',
            }
      }
      ref={messageInputRef}
    >
      {showMultiSessionSet && (
        <div className={styles.robotConfigWarp}>
          <div
            className={styles.robotConfig}
            onClick={() => updateBotConfigModal(true)}
          >
            <img src={configSetIcon} />
            <span>修改对话设置</span>
          </div>
        </div>
      )}
      <EditorProvider>
        <>
          <div
            className={styles.textWarp}
            onPaste={paste}
            onKeyDown={handleKeyDown}
            ref={pasteContainerRef}
            id="pasteContainerId"
            style={selectedCommand ? { display: 'none' } : {}}
          >
            {!typing && value === '' && !selectedCommand && (
              <div
                style={{
                  top: !isMultiSession ? '45px' : '5px',
                }}
                className={styles.placeholder}
              >
                {placeholderText}
              </div>
            )}
            {
              <ProsemirrorAdapterProvider>
                {hasInit && (
                  <EditMd
                    defaultValue={draftText?.value || draftText}
                    onFocus={() => setTypingStatus(true)}
                    onBlur={() => setTypingStatus(false)}
                    groupMemberList={grouMemberList}
                    ref={editMdRef}
                    onChange={(val: string) => {
                      setValue(val);
                    }}
                    onCompositionStart={handleCompositionStart}
                    onCompositionEnd={handleCompositionEnd}
                    showFormater={showFormater}
                    setShowFormater={setShowFormater}
                    uploadMsg={uploadMsg}
                    conversationID={conversation?.conversationID}
                    cloudUpload={cloudUpload}
                    isGroup={!!conversation?.groupID}
                    isMultiSession={isMultiSession}
                  />
                )}
              </ProsemirrorAdapterProvider>
            }
          </div>
          <>{ReplyPrefix}</>
          {fileList && fileList.length > 0 && (
            <FileRender fileList={fileList} setFileList={setFileList} />
          )}
          <RobotCommand
            ref={robotCommandRef}
            showFormater={showFormater}
            value={value}
            showCommandList={showCommandList}
            setShowCommandList={setShowCommandList}
            selectedCommand={selectedCommand}
            messageInputRef={messageInputRef}
            groupId={conversation?.groupID}
            botId={conversation?.userID}
            onClose={() => {
              setShowCommandList(false);
              setSelectedCommand(null);

              setFormInfo(undefined);
            }}
            onSelected={(command) => {
              setShowCommandList(false);
              setSelectedCommand(command);
              setReplyMsg(undefined);
            }}
            onSubmit={(values) => {
              botSubmit(values, conversation?.conversationType);
              setShowCommandList(false);
              setSelectedCommand(null);
            }}
            editMdRef={editMdRef}
            setFormInfo={setFormInfo}
            formInfo={formInfo}
          />
          <div
            className={styles.footer}
            style={selectedCommand ? { display: 'none' } : {}}
          >
            <TextOperate
              showFormater={showFormater}
              disabledBtn={false}
              activeMarks={editMdRef.current?.activeMarks}
            />
            <div
              className={classNames(
                styles.sendBtn,
                (showCommandList ||
                  value === '' ||
                  getCleanText(value) === '') &&
                  fileList.length === 0
                  ? llmLoading
                    ? styles.canSend
                    : ''
                  : styles.canSend
              )}
              onClick={() => {
                if (llmLoading) {
                  streamBreak();
                } else {
                  debounceFetch(value);
                }
              }}
            >
              {llmLoading ? (
                <img src={stopIcon} style={{ width: '20px' }} />
              ) : (
                <img style={{ width: '16px' }} src={sendIcon} />
              )}
            </div>
          </div>
        </>
      </EditorProvider>
      {botConfigModal && (
        <RobotConfigModal
          modalOpen={botConfigModal}
          oncancel={() => updateBotConfigModal(false)}
          botConfig={botConfig}
          defaultValue={currentBotConfig?.data || {}}
          showName={currentConversation?.showName || ''}
        />
      )}
    </div>
  );
};
export default MessageInput;
